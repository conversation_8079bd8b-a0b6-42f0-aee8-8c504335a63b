import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import ttest_ind
from scipy import stats

print("=== A/B ТЕСТ: АНАЛИЗ ЭФФЕКТИВНОСТИ РАСШИРЕНИЯ РАДИУСА ПОИСКА ===")
print()

# Загрузка данных
df = pd.read_csv("taxi_orders.csv", sep=';')
print(f"Загружено {len(df)} заказов")

# Преобразование временных колонок
time_columns = ['time_order', 'time_offer', 'time_assign', 'time_arrive', 'trip_time']
for col in time_columns:
    df[col] = pd.to_datetime(df[col], errors='coerce')

# Создание флагов для каждого этапа воронки
df['has_offer'] = df['time_offer'].notna()
df['has_assign'] = df['time_assign'].notna()
df['has_arrive'] = df['time_arrive'].notna()
df['has_trip'] = df['trip_time'].notna()

# Расчет времени до назначения (в минутах)
df['total_time_to_assign'] = (df['time_assign'] - df['time_order']).dt.total_seconds() / 60

print("\n=== РАСПРЕДЕЛЕНИЕ ПО ГРУППАМ ===")
group_distribution = df['test_group'].value_counts().sort_index()
print(f"Группа A (3 км): {group_distribution[0]} заказов ({group_distribution[0]/len(df)*100:.1f}%)")
print(f"Группа B (5 км): {group_distribution[1]} заказов ({group_distribution[1]/len(df)*100:.1f}%)")

# Анализ основной метрики - назначаемость
group_a = df[df['test_group'] == 0]
group_b = df[df['test_group'] == 1]

assign_rate_a = group_a['has_assign'].mean()
assign_rate_b = group_b['has_assign'].mean()
total_orders_a = len(group_a)
total_orders_b = len(group_b)
assigned_orders_a = group_a['has_assign'].sum()
assigned_orders_b = group_b['has_assign'].sum()

print("\n=== АНАЛИЗ НАЗНАЧАЕМОСТИ (ОСНОВНАЯ МЕТРИКА) ===")
print(f"Группа A (3 км): {assign_rate_a:.4f} ({assign_rate_a*100:.2f}%)")
print(f"Группа B (5 км): {assign_rate_b:.4f} ({assign_rate_b*100:.2f}%)")

diff_absolute = assign_rate_b - assign_rate_a
diff_relative = (assign_rate_b / assign_rate_a - 1) * 100

print(f"Абсолютная разница: {diff_absolute:.4f} ({diff_absolute*100:.2f} п.п.)")
print(f"Относительная разница: {diff_relative:.2f}%")

# Статистический тест для пропорций (Z-тест)
print("\n=== СТАТИСТИЧЕСКИЙ АНАЛИЗ ===")

# Альтернативная реализация Z-теста для пропорций
p1, p2 = assign_rate_a, assign_rate_b
n1, n2 = total_orders_a, total_orders_b

# Объединенная пропорция
p_pooled = (assigned_orders_a + assigned_orders_b) / (n1 + n2)

# Стандартная ошибка
se = np.sqrt(p_pooled * (1 - p_pooled) * (1/n1 + 1/n2))

# Z-статистика
z_stat = (p2 - p1) / se

# P-value (двусторонний тест)
p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))

print(f"Z-статистика: {z_stat:.4f}")
print(f"P-value: {p_value:.6f}")
print(f"Уровень значимости: 0.05")

if p_value < 0.05:
    print("✅ РЕЗУЛЬТАТ: Различие статистически значимо (p < 0.05)")
    print("   Расширение радиуса поиска значимо влияет на назначаемость")
else:
    print("❌ РЕЗУЛЬТАТ: Различие статистически НЕ значимо (p >= 0.05)")
    print("   Нет достаточных доказательств влияния расширения радиуса")

# Анализ времени до назначения
assigned_orders = df[df['has_assign'] == True]
time_a = assigned_orders[assigned_orders['test_group'] == 0]['total_time_to_assign']
time_b = assigned_orders[assigned_orders['test_group'] == 1]['total_time_to_assign']

# Удаляем выбросы (время > 60 минут)
time_a_clean = time_a[time_a <= 60]
time_b_clean = time_b[time_b <= 60]

print(f"\n=== АНАЛИЗ ВРЕМЕНИ ДО НАЗНАЧЕНИЯ ===")
print(f"Группа A: {time_a_clean.mean():.2f} мин (медиана: {time_a_clean.median():.2f} мин)")
print(f"Группа B: {time_b_clean.mean():.2f} мин (медиана: {time_b_clean.median():.2f} мин)")
print(f"Разница: {time_b_clean.mean() - time_a_clean.mean():.2f} мин")

# T-тест для времени
t_stat, t_p_value = ttest_ind(time_a_clean, time_b_clean, equal_var=False)
print(f"T-статистика: {t_stat:.4f}")
print(f"P-value: {t_p_value:.6f}")

if t_p_value < 0.05:
    print("✅ Различие во времени статистически значимо")
else:
    print("❌ Различие во времени статистически НЕ значимо")

# Бизнес-импакт
additional_assigns = (assign_rate_b - assign_rate_a) * total_orders_b
print(f"\n=== БИЗНЕС-ИМПАКТ ===")
print(f"Дополнительные назначения: {additional_assigns:.0f} заказов")
print(f"При среднем чеке 500₽: {additional_assigns * 500:.0f}₽ дополнительной выручки")

# Итоговые рекомендации
print(f"\n=== РЕКОМЕНДАЦИИ ===")
if p_value < 0.05 and diff_absolute > 0:
    print("✅ РЕКОМЕНДУЕТСЯ внедрить расширенный радиус поиска (5 км)")
    print("• Статистически значимое улучшение назначаемости")
    print("• Положительный бизнес-эффект")
elif p_value >= 0.05:
    print("⚠️  ТРЕБУЕТСЯ дополнительное тестирование")
    print("• Увеличить размер выборки или продлить период тестирования")
else:
    print("❌ НЕ РЕКОМЕНДУЕТСЯ внедрение")
    print("• Отрицательный эффект на основную метрику")