{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# A/B Тест: Ана<PERSON><PERSON>з эффективности расширения радиуса поиска водителей\n", "\n", "## Контекст\n", "Компания Central Taxi провела A/B тест для повышения назначаемости водителей:\n", "- **Группа A (контроль)**: радиус поиска 3 км (test_group = 0)\n", "- **Группа B (тест)**: расширенный радиус поиска 5 км (test_group = 1)\n", "\n", "**Цель**: определить, улуч<PERSON>ает ли расширение радиуса поиска назначаемость водителей."]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [], "source": ["# Импорт библиотек\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import ttest_ind\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Настройка графиков\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "plt.rcParams['font.size'] = 11"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Загрузка и анализ данных"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Общее количество заказов: 12281\n", "Размер датасета: (12281, 7)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_order</th>\n", "      <th>test_group</th>\n", "      <th>time_order</th>\n", "      <th>time_offer</th>\n", "      <th>time_assign</th>\n", "      <th>time_arrive</th>\n", "      <th>trip_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10001</td>\n", "      <td>0</td>\n", "      <td>2024/03/04 00:00:02</td>\n", "      <td>2024/03/04 00:00:43</td>\n", "      <td>2024/03/04 00:01:14</td>\n", "      <td>2024/03/04 00:09:07</td>\n", "      <td>2024/03/04 01:27:28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10002</td>\n", "      <td>0</td>\n", "      <td>2024/03/04 00:00:23</td>\n", "      <td>2024/03/04 00:01:00</td>\n", "      <td>2024/03/04 00:01:15</td>\n", "      <td>2024/03/04 00:08:45</td>\n", "      <td>2024/03/04 00:22:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10003</td>\n", "      <td>1</td>\n", "      <td>2024/03/04 00:07:09</td>\n", "      <td>2024/03/04 00:07:45</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10004</td>\n", "      <td>1</td>\n", "      <td>2024/03/04 00:07:12</td>\n", "      <td>2024/03/04 00:08:21</td>\n", "      <td>2024/03/04 00:08:47</td>\n", "      <td>2024/03/04 00:21:14</td>\n", "      <td>2024/03/04 01:23:25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10005</td>\n", "      <td>0</td>\n", "      <td>2024/03/04 00:12:00</td>\n", "      <td>2024/03/04 00:12:50</td>\n", "      <td>2024/03/04 00:13:09</td>\n", "      <td>2024/03/04 00:21:44</td>\n", "      <td>2024/03/04 00:46:45</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id_order  test_group           time_order           time_offer  \\\n", "0     10001           0  2024/03/04 00:00:02  2024/03/04 00:00:43   \n", "1     10002           0  2024/03/04 00:00:23  2024/03/04 00:01:00   \n", "2     10003           1  2024/03/04 00:07:09  2024/03/04 00:07:45   \n", "3     10004           1  2024/03/04 00:07:12  2024/03/04 00:08:21   \n", "4     10005           0  2024/03/04 00:12:00  2024/03/04 00:12:50   \n", "\n", "           time_assign          time_arrive            trip_time  \n", "0  2024/03/04 00:01:14  2024/03/04 00:09:07  2024/03/04 01:27:28  \n", "1  2024/03/04 00:01:15  2024/03/04 00:08:45  2024/03/04 00:22:22  \n", "2                  NaN                  NaN                  NaN  \n", "3  2024/03/04 00:08:47  2024/03/04 00:21:14  2024/03/04 01:23:25  \n", "4  2024/03/04 00:13:09  2024/03/04 00:21:44  2024/03/04 00:46:45  "]}, "execution_count": 176, "metadata": {}, "output_type": "execute_result"}], "source": ["# Загрузка данных\n", "data = pd.read_csv(\"taxi_orders.csv\", sep=';')\n", "\n", "print(f\"Общее количество заказов: {len(data)}\")\n", "print(f\"Размер датасета: {data.shape}\")\n", "data.head()"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Распределение заказов по группам:\n", "Группа A (3 км): 5684 заказов (46.3%)\n", "Группа B (5 км): 6597 заказов (53.7%)\n"]}], "source": ["# Распределение заказов по группам\n", "group_counts = data['test_group'].value_counts().sort_index()\n", "print(\"Распределение заказов по группам:\")\n", "print(f\"Группа A (3 км): {group_counts[0]} заказов ({group_counts[0]/len(data)*100:.1f}%)\")\n", "print(f\"Группа B (5 км): {group_counts[1]} заказов ({group_counts[1]/len(data)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Подготовка данных"]}, {"cell_type": "code", "execution_count": 178, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Процент успешности по этапам:\n", "Получили предложение: 87.8%\n", "Получили назначение: 78.4%\n", "Водитель прибыл: 62.9%\n", "Поездка завершена: 60.1%\n"]}], "source": ["# Преобразование временных колонок\n", "time_cols = ['time_order', 'time_offer', 'time_assign', 'time_arrive', 'trip_time']\n", "for col in time_cols:\n", "    data[col] = pd.to_datetime(data[col], errors='coerce')\n", "\n", "# Создание флагов успешности каждого этапа\n", "data['got_offer'] = data['time_offer'].notna()\n", "data['got_assigned'] = data['time_assign'].notna()\n", "data['driver_arrived'] = data['time_arrive'].notna()\n", "data['trip_completed'] = data['trip_time'].notna()\n", "\n", "print(\"Процент успешности по этапам:\")\n", "print(f\"Получили предложение: {data['got_offer'].mean()*100:.1f}%\")\n", "print(f\"Получили назначение: {data['got_assigned'].mean()*100:.1f}%\")\n", "print(f\"Водитель прибыл: {data['driver_arrived'].mean()*100:.1f}%\")\n", "print(f\"Поездка завершена: {data['trip_completed'].mean()*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON><PERSON><PERSON> основной метрики - назначаемость"]}, {"cell_type": "code", "execution_count": 179, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== АНАЛИЗ НАЗНАЧАЕМОСТИ ===\n", "Группа A (3 км): 0.7539 (75.39%)\n", "Группа B (5 км): 0.8093 (80.93%)\n", "\n", "Абсолютная разница: 0.0554 (5.54 п.п.)\n", "Относительная разница: 7.35%\n"]}], "source": ["# Разделение на группы\n", "group_a = data[data['test_group'] == 0]\n", "group_b = data[data['test_group'] == 1]\n", "\n", "# Расчет назначаемости по группам\n", "assign_rate_a = group_a['got_assigned'].mean()\n", "assign_rate_b = group_b['got_assigned'].mean()\n", "\n", "print(\"=== АНАЛИЗ НАЗНАЧАЕМОСТИ ===\")\n", "print(f\"Группа A (3 км): {assign_rate_a:.4f} ({assign_rate_a*100:.2f}%)\")\n", "print(f\"Группа B (5 км): {assign_rate_b:.4f} ({assign_rate_b*100:.2f}%)\")\n", "print()\n", "\n", "# Разница между группами\n", "diff_absolute = assign_rate_b - assign_rate_a\n", "diff_relative = (assign_rate_b / assign_rate_a - 1) * 100\n", "\n", "print(f\"Абсолютная разница: {diff_absolute:.4f} ({diff_absolute*100:.2f} п.п.)\")\n", "print(f\"Относительная разница: {diff_relative:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Статистический тест"]}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== СТАТИСТИЧЕСКИЙ ТЕСТ ===\n", "Z-статистика: 7.4395\n", "P-value: 0.000000\n", "Уровень значимости: 0.05\n", "\n", "✅ РЕЗУЛЬТАТ: Различие статистически значимо (p < 0.05)\n", "   Расширение радиуса поиска значимо влияет на назначаемость\n"]}], "source": ["# Z-тест для сравнения пропорций\n", "n1 = len(group_a)\n", "n2 = len(group_b)\n", "x1 = group_a['got_assigned'].sum()\n", "x2 = group_b['got_assigned'].sum()\n", "\n", "# Объединенная пропорция\n", "p_combined = (x1 + x2) / (n1 + n2)\n", "\n", "# Стандартная ошибка\n", "se = np.sqrt(p_combined * (1 - p_combined) * (1/n1 + 1/n2))\n", "\n", "# Z-статистика\n", "z_stat = (assign_rate_b - assign_rate_a) / se\n", "\n", "# P-value (двусторонний тест)\n", "p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))\n", "\n", "print(\"=== СТАТИСТИЧЕСКИЙ ТЕСТ ===\")\n", "print(f\"Z-статистика: {z_stat:.4f}\")\n", "print(f\"P-value: {p_value:.6f}\")\n", "print(f\"Уровень значимости: 0.05\")\n", "print()\n", "\n", "if p_value < 0.05:\n", "    print(\"✅ РЕЗУЛЬТАТ: Различие статистически значимо (p < 0.05)\")\n", "    print(\"   Расширение радиуса поиска значимо влияет на назначаемость\")\n", "else:\n", "    print(\"❌ РЕЗУЛЬТАТ: Различие статистически НЕ значимо (p >= 0.05)\")\n", "    print(\"   Нет достаточных доказательств влияния расширения радиуса\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Доверительный интервал"]}, {"cell_type": "code", "execution_count": 181, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ДОВЕРИТЕЛЬНЫЙ ИНТЕРВАЛ ===\n", "95% доверительный интервал для разности:\n", "[4.08%, 7.01%]\n", "\n", "✅ Нижняя граница > 0: улучшение гарантированно\n"]}], "source": ["# 95% доверительный интервал для разности пропорций\n", "se_diff = np.sqrt((assign_rate_a * (1 - assign_rate_a) / n1) + \n", "                  (assign_rate_b * (1 - assign_rate_b) / n2))\n", "\n", "margin_error = 1.96 * se_diff\n", "ci_lower = diff_absolute - margin_error\n", "ci_upper = diff_absolute + margin_error\n", "\n", "print(\"=== ДОВЕРИТЕЛЬНЫЙ ИНТЕРВАЛ ===\")\n", "print(f\"95% доверительный интервал для разности:\")\n", "print(f\"[{ci_lower*100:.2f}%, {ci_upper*100:.2f}%]\")\n", "print()\n", "\n", "if ci_lower > 0:\n", "    print(\"✅ Нижняя граница > 0: улучшение гарантированно\")\n", "elif ci_upper < 0:\n", "    print(\"❌ Верхняя граница < 0: ухудшение гарантированно\")\n", "else:\n", "    print(\"⚠️ Интервал включает 0: эффект неопределенный\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-импакт"]}, {"cell_type": "code", "execution_count": 182, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== БИЗНЕС-ИМПАКТ ===\n", "Дополнительные назначения: 366 заказов в неделю\n", "При среднем чеке 500₽:\n", "  • Неделя: 182858₽\n", "  • Месяц: 791776₽\n", "  • Год: 9508622₽\n"]}], "source": ["# Расчет бизнес-эффекта\n", "additional_assignments = diff_absolute * n2\n", "avg_order_value = 500  # средний чек заказа в рублях\n", "\n", "weekly_revenue = additional_assignments * avg_order_value\n", "monthly_revenue = weekly_revenue * 4.33\n", "yearly_revenue = weekly_revenue * 52\n", "\n", "print(\"=== БИЗНЕС-ИМПАКТ ===\")\n", "print(f\"Дополнительные назначения: {additional_assignments:.0f} заказов в неделю\")\n", "print(f\"При среднем чеке {avg_order_value}₽:\")\n", "print(f\"  • Неделя: {weekly_revenue:.0f}₽\")\n", "print(f\"  • Месяц: {monthly_revenue:.0f}₽\")\n", "print(f\"  • Год: {yearly_revenue:.0f}₽\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Визуализация результатов"]}, {"cell_type": "code", "execution_count": 183, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# График сравнения назначаемости\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# График 1: Сравнение назначаемости\n", "groups = ['Группа A\\n(3 км)', 'Группа B\\n(5 км)']\n", "rates = [assign_rate_a * 100, assign_rate_b * 100]\n", "colors = ['#FF6B6B', '#4ECDC4']\n", "\n", "bars = ax1.bar(groups, rates, color=colors, alpha=0.8)\n", "ax1.set_ylabel('Назначаемость (%)')\n", "ax1.set_title('Сравнение назначаемости по группам')\n", "ax1.grid(axis='y', alpha=0.3)\n", "\n", "# Добавляем значения на столбцы\n", "for bar, rate in zip(bars, rates):\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "             f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "# График 2: Воронка конверсии\n", "stages = ['Заказ', 'Предложение', 'Назначение', 'Прибытие']\n", "group_a_funnel = [100, \n", "                  group_a['got_offer'].mean() * 100,\n", "                  group_a['got_assigned'].mean() * 100,\n", "                  group_a['driver_arrived'].mean() * 100]\n", "group_b_funnel = [100,\n", "                  group_b['got_offer'].mean() * 100,\n", "                  group_b['got_assigned'].mean() * 100,\n", "                  group_b['driver_arrived'].mean() * 100]\n", "\n", "x = np.arange(len(stages))\n", "width = 0.35\n", "\n", "ax2.bar(x - width/2, group_a_funnel, width, label='Группа A (3 км)', \n", "        color=colors[0], alpha=0.8)\n", "ax2.bar(x + width/2, group_b_funnel, width, label='Группа B (5 км)', \n", "        color=colors[1], alpha=0.8)\n", "\n", "ax2.set_ylabel('Конверсия (%)')\n", "ax2.set_title('Воронка конверсии по этапам')\n", "ax2.set_xticks(x)\n", "ax2.set_xticklabels(stages)\n", "ax2.legend()\n", "ax2.grid(axis='y', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Выводы и рекомендации"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "                ИТОГОВЫЕ РЕЗУЛЬТАТЫ A/B ТЕСТА\n", "============================================================\n", "\n", "🎯 НАЗНАЧАЕМОСТЬ:\n", "   Группа A: 75.39% → Группа B: 80.93%\n", "   Улучшение: ***** п.п. (7.4%)\n", "\n", "📊 СТАТИСТИЧЕСКАЯ ЗНАЧИМОСТЬ:\n", "   P-value: 0.000000\n", "   ✅ Результат статистически значим\n", "\n", "💰 БИЗНЕС-ЭФФЕКТ:\n", "   Дополнительная выручка: 9508622₽ в год\n", "\n", "🎯 РЕКОМЕНДАЦИЯ:\n", "   ✅ ВНЕДРИТЬ расширенный радиус поиска (5 км)\n", "   • Статистически значимое улучшение\n", "   • Положительный бизнес-эффект\n", "============================================================\n"]}], "source": ["print(\"=\" * 60)\n", "print(\"                ИТОГОВЫЕ РЕЗУЛЬТАТЫ A/B ТЕСТА\")\n", "print(\"=\" * 60)\n", "print()\n", "print(f\"🎯 НАЗНАЧАЕМОСТЬ:\")\n", "print(f\"   Группа A: {assign_rate_a*100:.2f}% → Группа B: {assign_rate_b*100:.2f}%\")\n", "print(f\"   Улучшение: +{diff_absolute*100:.2f} п.п. ({diff_relative:.1f}%)\")\n", "print()\n", "print(f\"📊 СТАТИСТИЧЕСКАЯ ЗНАЧИМОСТЬ:\")\n", "print(f\"   P-value: {p_value:.6f}\")\n", "if p_value < 0.05:\n", "    print(\"   ✅ Результат статистически значим\")\n", "else:\n", "    print(\"   ❌ Результат статистически НЕ значим\")\n", "print()\n", "print(f\"💰 БИЗНЕС-ЭФФЕКТ:\")\n", "print(f\"   Дополнительная выручка: {yearly_revenue:.0f}₽ в год\")\n", "print()\n", "print(f\"🎯 РЕКОМЕНДАЦИЯ:\")\n", "if p_value < 0.05 and diff_absolute > 0:\n", "    print(\"   ✅ ВНЕДРИТЬ расширенный радиус поиска (5 км)\")\n", "    print(\"   • Статистически значимое улучшение\")\n", "    print(\"   • Положительный бизнес-эффект\")\n", "elif p_value >= 0.05:\n", "    print(\"   ⚠️ ТРЕБУЕТСЯ дополнительное тестирование\")\n", "    print(\"   • Увеличить размер выборки\")\n", "else:\n", "    print(\"   ❌ НЕ РЕКОМЕНДУЕТСЯ внедрение\")\n", "print(\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}