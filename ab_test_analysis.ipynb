# Импорт библиотек
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import ttest_ind
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Настройка графиков
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 11

# Загрузка данных
data = pd.read_csv("taxi_orders.csv", sep=';')

print(f"Общее количество заказов: {len(data)}")
print(f"Размер датасета: {data.shape}")
data.head()

# Распределение заказов по группам
group_counts = data['test_group'].value_counts().sort_index()
print("Распределение заказов по группам:")
print(f"Группа A (3 км): {group_counts[0]} заказов ({group_counts[0]/len(data)*100:.1f}%)")
print(f"Группа B (5 км): {group_counts[1]} заказов ({group_counts[1]/len(data)*100:.1f}%)")

# Преобразование временных колонок
time_cols = ['time_order', 'time_offer', 'time_assign', 'time_arrive', 'trip_time']
for col in time_cols:
    data[col] = pd.to_datetime(data[col], errors='coerce')

# Создание флагов успешности каждого этапа
data['got_offer'] = data['time_offer'].notna()
data['got_assigned'] = data['time_assign'].notna()
data['driver_arrived'] = data['time_arrive'].notna()
data['trip_completed'] = data['trip_time'].notna()

print("Процент успешности по этапам:")
print(f"Получили предложение: {data['got_offer'].mean()*100:.1f}%")
print(f"Получили назначение: {data['got_assigned'].mean()*100:.1f}%")
print(f"Водитель прибыл: {data['driver_arrived'].mean()*100:.1f}%")
print(f"Поездка завершена: {data['trip_completed'].mean()*100:.1f}%")

# Разделение на группы
group_a = data[data['test_group'] == 0]
group_b = data[data['test_group'] == 1]

# Расчет назначаемости по группам
assign_rate_a = group_a['got_assigned'].mean()
assign_rate_b = group_b['got_assigned'].mean()

print("=== АНАЛИЗ НАЗНАЧАЕМОСТИ ===")
print(f"Группа A (3 км): {assign_rate_a:.4f} ({assign_rate_a*100:.2f}%)")
print(f"Группа B (5 км): {assign_rate_b:.4f} ({assign_rate_b*100:.2f}%)")
print()

# Разница между группами
diff_absolute = assign_rate_b - assign_rate_a
diff_relative = (assign_rate_b / assign_rate_a - 1) * 100

print(f"Абсолютная разница: {diff_absolute:.4f} ({diff_absolute*100:.2f} п.п.)")
print(f"Относительная разница: {diff_relative:.2f}%")

# Z-тест для сравнения пропорций
n1 = len(group_a)
n2 = len(group_b)
x1 = group_a['got_assigned'].sum()
x2 = group_b['got_assigned'].sum()

# Объединенная пропорция
p_combined = (x1 + x2) / (n1 + n2)

# Стандартная ошибка
se = np.sqrt(p_combined * (1 - p_combined) * (1/n1 + 1/n2))

# Z-статистика
z_stat = (assign_rate_b - assign_rate_a) / se

# P-value (двусторонний тест)
p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))

print("=== СТАТИСТИЧЕСКИЙ ТЕСТ ===")
print(f"Z-статистика: {z_stat:.4f}")
print(f"P-value: {p_value:.6f}")
print(f"Уровень значимости: 0.05")
print()

if p_value < 0.05:
    print("✅ РЕЗУЛЬТАТ: Различие статистически значимо (p < 0.05)")
    print("   Расширение радиуса поиска значимо влияет на назначаемость")
else:
    print("❌ РЕЗУЛЬТАТ: Различие статистически НЕ значимо (p >= 0.05)")
    print("   Нет достаточных доказательств влияния расширения радиуса")

# 95% доверительный интервал для разности пропорций
se_diff = np.sqrt((assign_rate_a * (1 - assign_rate_a) / n1) + 
                  (assign_rate_b * (1 - assign_rate_b) / n2))

margin_error = 1.96 * se_diff
ci_lower = diff_absolute - margin_error
ci_upper = diff_absolute + margin_error

print("=== ДОВЕРИТЕЛЬНЫЙ ИНТЕРВАЛ ===")
print(f"95% доверительный интервал для разности:")
print(f"[{ci_lower*100:.2f}%, {ci_upper*100:.2f}%]")
print()

if ci_lower > 0:
    print("✅ Нижняя граница > 0: улучшение гарантированно")
elif ci_upper < 0:
    print("❌ Верхняя граница < 0: ухудшение гарантированно")
else:
    print("⚠️ Интервал включает 0: эффект неопределенный")

# Расчет бизнес-эффекта
additional_assignments = diff_absolute * n2
avg_order_value = 500  # средний чек заказа в рублях

weekly_revenue = additional_assignments * avg_order_value
monthly_revenue = weekly_revenue * 4.33
yearly_revenue = weekly_revenue * 52

print("=== БИЗНЕС-ИМПАКТ ===")
print(f"Дополнительные назначения: {additional_assignments:.0f} заказов в неделю")
print(f"При среднем чеке {avg_order_value}₽:")
print(f"  • Неделя: {weekly_revenue:.0f}₽")
print(f"  • Месяц: {monthly_revenue:.0f}₽")
print(f"  • Год: {yearly_revenue:.0f}₽")

# График сравнения назначаемости
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# График 1: Сравнение назначаемости
groups = ['Группа A\n(3 км)', 'Группа B\n(5 км)']
rates = [assign_rate_a * 100, assign_rate_b * 100]
colors = ['#FF6B6B', '#4ECDC4']

bars = ax1.bar(groups, rates, color=colors, alpha=0.8)
ax1.set_ylabel('Назначаемость (%)')
ax1.set_title('Сравнение назначаемости по группам')
ax1.grid(axis='y', alpha=0.3)

# Добавляем значения на столбцы
for bar, rate in zip(bars, rates):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
             f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

# График 2: Воронка конверсии
stages = ['Заказ', 'Предложение', 'Назначение', 'Прибытие']
group_a_funnel = [100, 
                  group_a['got_offer'].mean() * 100,
                  group_a['got_assigned'].mean() * 100,
                  group_a['driver_arrived'].mean() * 100]
group_b_funnel = [100,
                  group_b['got_offer'].mean() * 100,
                  group_b['got_assigned'].mean() * 100,
                  group_b['driver_arrived'].mean() * 100]

x = np.arange(len(stages))
width = 0.35

ax2.bar(x - width/2, group_a_funnel, width, label='Группа A (3 км)', 
        color=colors[0], alpha=0.8)
ax2.bar(x + width/2, group_b_funnel, width, label='Группа B (5 км)', 
        color=colors[1], alpha=0.8)

ax2.set_ylabel('Конверсия (%)')
ax2.set_title('Воронка конверсии по этапам')
ax2.set_xticks(x)
ax2.set_xticklabels(stages)
ax2.legend()
ax2.grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

print("=" * 60)
print("                ИТОГОВЫЕ РЕЗУЛЬТАТЫ A/B ТЕСТА")
print("=" * 60)
print()
print(f"🎯 НАЗНАЧАЕМОСТЬ:")
print(f"   Группа A: {assign_rate_a*100:.2f}% → Группа B: {assign_rate_b*100:.2f}%")
print(f"   Улучшение: +{diff_absolute*100:.2f} п.п. ({diff_relative:.1f}%)")
print()
print(f"📊 СТАТИСТИЧЕСКАЯ ЗНАЧИМОСТЬ:")
print(f"   P-value: {p_value:.6f}")
if p_value < 0.05:
    print("   ✅ Результат статистически значим")
else:
    print("   ❌ Результат статистически НЕ значим")
print()
print(f"💰 БИЗНЕС-ЭФФЕКТ:")
print(f"   Дополнительная выручка: {yearly_revenue:.0f}₽ в год")
print()
print(f"🎯 РЕКОМЕНДАЦИЯ:")
if p_value < 0.05 and diff_absolute > 0:
    print("   ✅ ВНЕДРИТЬ расширенный радиус поиска (5 км)")
    print("   • Статистически значимое улучшение")
    print("   • Положительный бизнес-эффект")
elif p_value >= 0.05:
    print("   ⚠️ ТРЕБУЕТСЯ дополнительное тестирование")
    print("   • Увеличить размер выборки")
else:
    print("   ❌ НЕ РЕКОМЕНДУЕТСЯ внедрение")
print("=" * 60)