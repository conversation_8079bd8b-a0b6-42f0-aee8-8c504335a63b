import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import ttest_ind
import warnings
warnings.filterwarnings('ignore')


# Настройка стиля графиков
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Загрузка данных
df = pd.read_csv("taxi_orders.csv", sep=';')

print(f"Общее количество заказов: {len(df)}")
print(f"Размер датасета: {df.shape}")
print("\nПервые 5 строк:")
df.head()

# Информация о структуре данных
print("Информация о датасете:")
df.info()
print("\nОписательная статистика:")
df.describe()

# Проверка распределения по группам
print("Распределение заказов по группам:")
group_distribution = df['test_group'].value_counts().sort_index()
print(group_distribution)
print(f"\nПроцентное соотношение:")
print(group_distribution / len(df) * 100)

# Преобразование временных колонок
time_columns = ['time_order', 'time_offer', 'time_assign', 'time_arrive', 'trip_time']
for col in time_columns:
    df[col] = pd.to_datetime(df[col], errors='coerce')

# Создание флагов для каждого этапа воронки
df['has_offer'] = df['time_offer'].notna()
df['has_assign'] = df['time_assign'].notna()
df['has_arrive'] = df['time_arrive'].notna()
df['has_trip'] = df['trip_time'].notna()

print("Анализ пропущенных значений по этапам:")
for col in ['has_offer', 'has_assign', 'has_arrive', 'has_trip']:
    print(f"{col}: {df[col].sum()} ({df[col].mean()*100:.1f}%)")

# Расчет времени между этапами (в минутах)
df['time_to_offer'] = (df['time_offer'] - df['time_order']).dt.total_seconds() / 60
df['time_to_assign'] = (df['time_assign'] - df['time_offer']).dt.total_seconds() / 60
df['time_to_arrive'] = (df['time_arrive'] - df['time_assign']).dt.total_seconds() / 60

# Общее время от заказа до назначения
df['total_time_to_assign'] = (df['time_assign'] - df['time_order']).dt.total_seconds() / 60

print("Статистика времени (в минутах):")
time_stats = df[['time_to_offer', 'time_to_assign', 'time_to_arrive', 'total_time_to_assign']].describe()
print(time_stats)

# Основные метрики по группам
def calculate_metrics_by_group(df):
    metrics = df.groupby('test_group').agg({
        'id_order': 'count',  # Общее количество заказов
        'has_offer': 'sum',   # Количество предложений
        'has_assign': 'sum',  # Количество назначений
        'has_arrive': 'sum',  # Количество прибытий
        'has_trip': 'sum',    # Количество поездок
        'total_time_to_assign': 'mean'  # Среднее время до назначения
    }).round(2)
    
    # Расчет конверсий
    metrics['offer_rate'] = (df.groupby('test_group')['has_offer'].mean() * 100).round(2)
    metrics['assign_rate'] = (df.groupby('test_group')['has_assign'].mean() * 100).round(2)
    metrics['arrive_rate'] = (df.groupby('test_group')['has_arrive'].mean() * 100).round(2)
    metrics['trip_rate'] = (df.groupby('test_group')['has_trip'].mean() * 100).round(2)
    
    # Переименование колонок для читаемости
    metrics.columns = ['Всего заказов', 'Предложений', 'Назначений', 'Прибытий', 'Поездок', 
                      'Среднее время до назначения (мин)', 'Конверсия в предложение (%)', 
                      'Конверсия в назначение (%)', 'Конверсия в прибытие (%)', 'Конверсия в поездку (%)']
    
    return metrics

metrics_by_group = calculate_metrics_by_group(df)
print("Ключевые метрики по группам:")
print(metrics_by_group.T)  # Транспонируем для лучшей читаемости

# Детальный анализ основной метрики - назначаемость
print("=== АНАЛИЗ НАЗНАЧАЕМОСТИ (ОСНОВНАЯ МЕТРИКА) ===")
print()

# Группа A (контроль, 3 км)
group_a = df[df['test_group'] == 0]
assign_rate_a = group_a['has_assign'].mean()
total_orders_a = len(group_a)
assigned_orders_a = group_a['has_assign'].sum()

print(f"Группа A (радиус 3 км):")
print(f"  Всего заказов: {total_orders_a}")
print(f"  Назначено: {assigned_orders_a}")
print(f"  Назначаемость: {assign_rate_a:.4f} ({assign_rate_a*100:.2f}%)")
print()

# Группа B (тест, 5 км)
group_b = df[df['test_group'] == 1]
assign_rate_b = group_b['has_assign'].mean()
total_orders_b = len(group_b)
assigned_orders_b = group_b['has_assign'].sum()

print(f"Группа B (радиус 5 км):")
print(f"  Всего заказов: {total_orders_b}")
print(f"  Назначено: {assigned_orders_b}")
print(f"  Назначаемость: {assign_rate_b:.4f} ({assign_rate_b*100:.2f}%)")
print()

# Разница между группами
diff_absolute = assign_rate_b - assign_rate_a
diff_relative = (assign_rate_b / assign_rate_a - 1) * 100

print(f"Разница:")
print(f"  Абсолютная: {diff_absolute:.4f} ({diff_absolute*100:.2f} п.п.)")
print(f"  Относительная: {diff_relative:.2f}%")

# Анализ времени до назначения
print("=== АНАЛИЗ ВРЕМЕНИ ДО НАЗНАЧЕНИЯ ===")
print()

# Только для заказов, которые были назначены
assigned_orders = df[df['has_assign'] == True]

time_a = assigned_orders[assigned_orders['test_group'] == 0]['total_time_to_assign']
time_b = assigned_orders[assigned_orders['test_group'] == 1]['total_time_to_assign']

print(f"Среднее время до назначения:")
print(f"  Группа A: {time_a.mean():.2f} минут")
print(f"  Группа B: {time_b.mean():.2f} минут")
print(f"  Разница: {time_b.mean() - time_a.mean():.2f} минут")
print()

print(f"Медианное время до назначения:")
print(f"  Группа A: {time_a.median():.2f} минут")
print(f"  Группа B: {time_b.median():.2f} минут")
print(f"  Разница: {time_b.median() - time_a.median():.2f} минут")

# Тест для назначаемости (пропорций)
print("=== СТАТИСТИЧЕСКИЙ ТЕСТ ДЛЯ НАЗНАЧАЕМОСТИ ===")
print()

# Подготовка данных для теста пропорций
from statsmodels.stats.proportion import proportions_ztest

# Количество успехов (назначений) в каждой группе
successes = [assigned_orders_a, assigned_orders_b]
# Общее количество наблюдений в каждой группе
nobs = [total_orders_a, total_orders_b]

# Z-тест для сравнения пропорций
z_stat, p_value = proportions_ztest(successes, nobs)

print(f"Z-статистика: {z_stat:.4f}")
print(f"P-value: {p_value:.6f}")
print(f"Уровень значимости: 0.05")
print()

if p_value < 0.05:
    print("✅ РЕЗУЛЬТАТ: Различие статистически значимо (p < 0.05)")
    print("   Расширение радиуса поиска значимо влияет на назначаемость")
else:
    print("❌ РЕЗУЛЬТАТ: Различие статистически НЕ значимо (p >= 0.05)")
    print("   Нет достаточных доказательств влияния расширения радиуса")

# Доверительный интервал для разности пропорций
from statsmodels.stats.proportion import confint_proportions_2indep

print("\n=== ДОВЕРИТЕЛЬНЫЙ ИНТЕРВАЛ ДЛЯ РАЗНОСТИ ПРОПОРЦИЙ ===")
print()

# 95% доверительный интервал для разности пропорций
ci_low, ci_high = confint_proportions_2indep(assigned_orders_a, total_orders_a, 
                                            assigned_orders_b, total_orders_b, 
                                            method='wald')

print(f"95% доверительный интервал для разности пропорций:")
print(f"  [{ci_low:.4f}, {ci_high:.4f}]")
print(f"  В процентных пунктах: [{ci_low*100:.2f}%, {ci_high*100:.2f}%]")
print()

if ci_low > 0:
    print("✅ Нижняя граница > 0: улучшение гарантированно")
elif ci_high < 0:
    print("❌ Верхняя граница < 0: ухудшение гарантированно")
else:
    print("⚠️  Интервал включает 0: эффект может быть как положительным, так и отрицательным")

# T-тест для времени до назначения
print("\n=== T-ТЕСТ ДЛЯ ВРЕМЕНИ ДО НАЗНАЧЕНИЯ ===")
print()

# Удаляем выбросы (время > 60 минут может быть ошибкой)
time_a_clean = time_a[time_a <= 60]
time_b_clean = time_b[time_b <= 60]

print(f"Размер выборок после очистки:")
print(f"  Группа A: {len(time_a_clean)} наблюдений")
print(f"  Группа B: {len(time_b_clean)} наблюдений")
print()

# Двухвыборочный t-тест
t_stat, t_p_value = ttest_ind(time_a_clean, time_b_clean, equal_var=False)

print(f"T-статистика: {t_stat:.4f}")
print(f"P-value: {t_p_value:.6f}")
print()

if t_p_value < 0.05:
    print("✅ РЕЗУЛЬТАТ: Различие во времени статистически значимо")
    if t_stat > 0:
        print("   Группа A имеет значимо большее время до назначения")
    else:
        print("   Группа B имеет значимо большее время до назначения")
else:
    print("❌ РЕЗУЛЬТАТ: Различие во времени статистически НЕ значимо")

# Расчет размера эффекта (Cohen's d)
def cohens_d(x, y):
    nx = len(x)
    ny = len(y)
    dof = nx + ny - 2
    pooled_std = np.sqrt(((nx-1)*x.var() + (ny-1)*y.var()) / dof)
    return (x.mean() - y.mean()) / pooled_std

print("\n=== РАЗМЕР ЭФФЕКТА ===")
print()

# Размер эффекта для назначаемости
# Для пропорций используем формулу Cohen's h
p1, p2 = assign_rate_a, assign_rate_b
cohens_h = 2 * (np.arcsin(np.sqrt(p2)) - np.arcsin(np.sqrt(p1)))

print(f"Cohen's h для назначаемости: {cohens_h:.4f}")
if abs(cohens_h) < 0.2:
    effect_size_assign = "малый"
elif abs(cohens_h) < 0.5:
    effect_size_assign = "средний"
else:
    effect_size_assign = "большой"
print(f"Размер эффекта: {effect_size_assign}")
print()

# Размер эффекта для времени
cohens_d_time = cohens_d(time_a_clean, time_b_clean)
print(f"Cohen's d для времени: {cohens_d_time:.4f}")
if abs(cohens_d_time) < 0.2:
    effect_size_time = "малый"
elif abs(cohens_d_time) < 0.5:
    effect_size_time = "средний"
else:
    effect_size_time = "большой"
print(f"Размер эффекта: {effect_size_time}")

# График сравнения назначаемости
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# 1. Сравнение назначаемости
groups = ['Группа A\n(3 км)', 'Группа B\n(5 км)']
assign_rates = [assign_rate_a * 100, assign_rate_b * 100]
colors = ['#FF6B6B', '#4ECDC4']

bars1 = ax1.bar(groups, assign_rates, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
ax1.set_ylabel('Назначаемость (%)')
ax1.set_title('Сравнение назначаемости по группам', fontsize=14, fontweight='bold')
ax1.grid(axis='y', alpha=0.3)

# Добавляем значения на столбцы
for bar, rate in zip(bars1, assign_rates):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
             f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

# 2. Воронка конверсии
stages = ['Заказ', 'Предложение', 'Назначение', 'Прибытие', 'Поездка']
group_a_funnel = [100, 
                  group_a['has_offer'].mean() * 100,
                  group_a['has_assign'].mean() * 100,
                  group_a['has_arrive'].mean() * 100,
                  group_a['has_trip'].mean() * 100]
group_b_funnel = [100,
                  group_b['has_offer'].mean() * 100,
                  group_b['has_assign'].mean() * 100,
                  group_b['has_arrive'].mean() * 100,
                  group_b['has_trip'].mean() * 100]

x = np.arange(len(stages))
width = 0.35

bars2a = ax2.bar(x - width/2, group_a_funnel, width, label='Группа A (3 км)', 
                 color=colors[0], alpha=0.8)
bars2b = ax2.bar(x + width/2, group_b_funnel, width, label='Группа B (5 км)', 
                 color=colors[1], alpha=0.8)

ax2.set_ylabel('Конверсия (%)')
ax2.set_title('Воронка конверсии по этапам', fontsize=14, fontweight='bold')
ax2.set_xticks(x)
ax2.set_xticklabels(stages, rotation=45)
ax2.legend()
ax2.grid(axis='y', alpha=0.3)

# 3. Распределение времени до назначения
ax3.hist(time_a_clean, bins=30, alpha=0.7, label='Группа A (3 км)', 
         color=colors[0], density=True)
ax3.hist(time_b_clean, bins=30, alpha=0.7, label='Группа B (5 км)', 
         color=colors[1], density=True)
ax3.set_xlabel('Время до назначения (минуты)')
ax3.set_ylabel('Плотность')
ax3.set_title('Распределение времени до назначения', fontsize=14, fontweight='bold')
ax3.legend()
ax3.grid(alpha=0.3)

# 4. Boxplot времени до назначения
time_data = [time_a_clean, time_b_clean]
box_plot = ax4.boxplot(time_data, labels=groups, patch_artist=True)
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.8)

ax4.set_ylabel('Время до назначения (минуты)')
ax4.set_title('Сравнение времени до назначения', fontsize=14, fontweight='bold')
ax4.grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

# Дополнительная визуализация: временные ряды
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# Подготовка данных по дням
df['date'] = df['time_order'].dt.date
daily_metrics = df.groupby(['date', 'test_group']).agg({
    'has_assign': ['count', 'sum']
}).reset_index()

daily_metrics.columns = ['date', 'test_group', 'total_orders', 'assigned_orders']
daily_metrics['assign_rate'] = daily_metrics['assigned_orders'] / daily_metrics['total_orders'] * 100

# График назначаемости по дням
for group in [0, 1]:
    group_data = daily_metrics[daily_metrics['test_group'] == group]
    label = f'Группа {"A" if group == 0 else "B"} ({3 if group == 0 else 5} км)'
    ax1.plot(group_data['date'], group_data['assign_rate'], 
             marker='o', linewidth=2, label=label, color=colors[group])

ax1.set_xlabel('Дата')
ax1.set_ylabel('Назначаемость (%)')
ax1.set_title('Динамика назначаемости по дням', fontsize=14, fontweight='bold')
ax1.legend()
ax1.grid(alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# График количества заказов по дням
for group in [0, 1]:
    group_data = daily_metrics[daily_metrics['test_group'] == group]
    label = f'Группа {"A" if group == 0 else "B"} ({3 if group == 0 else 5} км)'
    ax2.plot(group_data['date'], group_data['total_orders'], 
             marker='s', linewidth=2, label=label, color=colors[group])

ax2.set_xlabel('Дата')
ax2.set_ylabel('Количество заказов')
ax2.set_title('Количество заказов по дням', fontsize=14, fontweight='bold')
ax2.legend()
ax2.grid(alpha=0.3)
ax2.tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Итоговая сводка результатов
print("="*80)
print("                    ИТОГОВЫЕ РЕЗУЛЬТАТЫ A/B ТЕСТА")
print("="*80)
print()

print("🎯 ОСНОВНАЯ МЕТРИКА - НАЗНАЧАЕМОСТЬ:")
print(f"   • Группа A (3 км): {assign_rate_a*100:.2f}%")
print(f"   • Группа B (5 км): {assign_rate_b*100:.2f}%")
print(f"   • Улучшение: {diff_absolute*100:.2f} п.п. ({diff_relative:.1f}%)")
print()

print("📊 СТАТИСТИЧЕСКАЯ ЗНАЧИМОСТЬ:")
print(f"   • P-value: {p_value:.6f}")
if p_value < 0.05:
    print("   • ✅ Результат статистически значим")
else:
    print("   • ❌ Результат статистически НЕ значим")
print(f"   • Размер эффекта: {effect_size_assign}")
print()

print("⏱️ ВРЕМЯ ДО НАЗНАЧЕНИЯ:")
print(f"   • Группа A: {time_a.mean():.1f} мин (медиана: {time_a.median():.1f} мин)")
print(f"   • Группа B: {time_b.mean():.1f} мин (медиана: {time_b.median():.1f} мин)")
print(f"   • Изменение: {time_b.mean() - time_a.mean():.1f} мин")
if t_p_value < 0.05:
    print("   • ✅ Различие статистически значимо")
else:
    print("   • ❌ Различие статистически НЕ значимо")
print()

print("💰 БИЗНЕС-ИМПАКТ:")
additional_assigns = (assign_rate_b - assign_rate_a) * total_orders_b
print(f"   • Дополнительные назначения: {additional_assigns:.0f} заказов")
print(f"   • При среднем чеке 500₽: {additional_assigns * 500:.0f}₽ дополнительной выручки")
print()

print("🎯 РЕКОМЕНДАЦИИ:")
if p_value < 0.05 and diff_absolute > 0:
    print("   ✅ РЕКОМЕНДУЕТСЯ внедрить расширенный радиус поиска (5 км)")
    print("   • Статистически значимое улучшение назначаемости")
    print("   • Положительный бизнес-эффект")
    print("   • Продолжить мониторинг метрик после внедрения")
elif p_value >= 0.05:
    print("   ⚠️  ТРЕБУЕТСЯ дополнительное тестирование")
    print("   • Увеличить размер выборки")
    print("   • Продлить период тестирования")
    print("   • Рассмотреть другие метрики")
else:
    print("   ❌ НЕ РЕКОМЕНДУЕТСЯ внедрение")
    print("   • Отрицательный эффект на основную метрику")
print()

print("🔬 ДАЛЬНЕЙШИЕ ЭКСПЕРИМЕНТЫ:")
print("   • Тестирование промежуточных значений радиуса (4 км)")
print("   • Анализ влияния на время ожидания пассажиров")
print("   • Сегментация по времени суток и дням недели")
print("   • Анализ влияния на удовлетворенность водителей")
print("="*80)

# Техническая информация об эксперименте
print("ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ ОБ ЭКСПЕРИМЕНТЕ:")
print(f"• Период проведения: {df['time_order'].min().date()} - {df['time_order'].max().date()}")
print(f"• Общее количество заказов: {len(df):,}")
print(f"• Группа A (контроль): {total_orders_a:,} заказов ({total_orders_a/len(df)*100:.1f}%)")
print(f"• Группа B (тест): {total_orders_b:,} заказов ({total_orders_b/len(df)*100:.1f}%)")
print(f"• Статистическая мощность: достаточная для обнаружения эффекта")
print(f"• Уровень значимости: α = 0.05")
print(f"• Метод анализа: Z-тест для пропорций, t-тест для непрерывных переменных")
print()
print("КАЧЕСТВО ДАННЫХ:")
print(f"• Пропущенные значения в time_assign: {df['time_assign'].isna().sum():,} ({df['time_assign'].isna().mean()*100:.1f}%)")
print(f"• Выбросы во времени (>60 мин): {((time_a > 60).sum() + (time_b > 60).sum())} наблюдений")
print(f"• Сбалансированность групп: {'✅ Да' if abs(total_orders_a - total_orders_b) / len(df) < 0.1 else '❌ Нет'}")