import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import ttest_ind
from scipy import stats

# Настройка стиля графиков
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 10)
plt.rcParams['font.size'] = 12

# Загрузка и подготовка данных
df = pd.read_csv("taxi_orders.csv", sep=';')

time_columns = ['time_order', 'time_offer', 'time_assign', 'time_arrive', 'trip_time']
for col in time_columns:
    df[col] = pd.to_datetime(df[col], errors='coerce')

df['has_offer'] = df['time_offer'].notna()
df['has_assign'] = df['time_assign'].notna()
df['has_arrive'] = df['time_arrive'].notna()
df['has_trip'] = df['trip_time'].notna()
df['total_time_to_assign'] = (df['time_assign'] - df['time_order']).dt.total_seconds() / 60

# Разделение на группы
group_a = df[df['test_group'] == 0]
group_b = df[df['test_group'] == 1]

assign_rate_a = group_a['has_assign'].mean()
assign_rate_b = group_b['has_assign'].mean()

# Время до назначения (только для назначенных заказов)
assigned_orders = df[df['has_assign'] == True]
time_a = assigned_orders[assigned_orders['test_group'] == 0]['total_time_to_assign']
time_b = assigned_orders[assigned_orders['test_group'] == 1]['total_time_to_assign']
time_a_clean = time_a[time_a <= 60]
time_b_clean = time_b[time_b <= 60]

# Создание графиков
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
colors = ['#FF6B6B', '#4ECDC4']

# 1. Сравнение назначаемости
groups = ['Группа A\n(3 км)', 'Группа B\n(5 км)']
assign_rates = [assign_rate_a * 100, assign_rate_b * 100]

bars1 = ax1.bar(groups, assign_rates, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
ax1.set_ylabel('Назначаемость (%)')
ax1.set_title('Сравнение назначаемости по группам', fontsize=16, fontweight='bold')
ax1.grid(axis='y', alpha=0.3)
ax1.set_ylim(0, 100)

# Добавляем значения на столбцы
for bar, rate in zip(bars1, assign_rates):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=14)

# Добавляем разность
diff_text = f'+{(assign_rate_b - assign_rate_a)*100:.1f} п.п.'
ax1.text(0.5, max(assign_rates) - 10, diff_text, ha='center', va='center', 
         fontsize=14, fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

# 2. Воронка конверсии
stages = ['Заказ', 'Предложение', 'Назначение', 'Прибытие', 'Поездка']
group_a_funnel = [100, 
                  group_a['has_offer'].mean() * 100,
                  group_a['has_assign'].mean() * 100,
                  group_a['has_arrive'].mean() * 100,
                  group_a['has_trip'].mean() * 100]
group_b_funnel = [100,
                  group_b['has_offer'].mean() * 100,
                  group_b['has_assign'].mean() * 100,
                  group_b['has_arrive'].mean() * 100,
                  group_b['has_trip'].mean() * 100]

x = np.arange(len(stages))
width = 0.35

bars2a = ax2.bar(x - width/2, group_a_funnel, width, label='Группа A (3 км)', 
                 color=colors[0], alpha=0.8)
bars2b = ax2.bar(x + width/2, group_b_funnel, width, label='Группа B (5 км)', 
                 color=colors[1], alpha=0.8)

ax2.set_ylabel('Конверсия (%)')
ax2.set_title('Воронка конверсии по этапам', fontsize=16, fontweight='bold')
ax2.set_xticks(x)
ax2.set_xticklabels(stages, rotation=45)
ax2.legend(fontsize=12)
ax2.grid(axis='y', alpha=0.3)

# Добавляем значения на столбцы воронки
for i, (a_val, b_val) in enumerate(zip(group_a_funnel, group_b_funnel)):
    ax2.text(i - width/2, a_val + 1, f'{a_val:.1f}%', ha='center', va='bottom', fontsize=10)
    ax2.text(i + width/2, b_val + 1, f'{b_val:.1f}%', ha='center', va='bottom', fontsize=10)

# 3. Распределение времени до назначения
ax3.hist(time_a_clean, bins=30, alpha=0.7, label='Группа A (3 км)', 
         color=colors[0], density=True)
ax3.hist(time_b_clean, bins=30, alpha=0.7, label='Группа B (5 км)', 
         color=colors[1], density=True)
ax3.set_xlabel('Время до назначения (минуты)')
ax3.set_ylabel('Плотность')
ax3.set_title('Распределение времени до назначения', fontsize=16, fontweight='bold')
ax3.legend(fontsize=12)
ax3.grid(alpha=0.3)

# Добавляем средние значения
ax3.axvline(time_a_clean.mean(), color=colors[0], linestyle='--', linewidth=2, alpha=0.8)
ax3.axvline(time_b_clean.mean(), color=colors[1], linestyle='--', linewidth=2, alpha=0.8)

# 4. Boxplot времени до назначения
time_data = [time_a_clean, time_b_clean]
box_plot = ax4.boxplot(time_data, labels=groups, patch_artist=True)
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.8)

ax4.set_ylabel('Время до назначения (минуты)')
ax4.set_title('Сравнение времени до назначения', fontsize=16, fontweight='bold')
ax4.grid(axis='y', alpha=0.3)

# Добавляем статистику
stats_text = f'Группа A: {time_a_clean.mean():.2f} мин\nГруппа B: {time_b_clean.mean():.2f} мин'
ax4.text(0.02, 0.98, stats_text, transform=ax4.transAxes, fontsize=11,
         verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

plt.tight_layout()
plt.savefig('ab_test_results.png', dpi=300, bbox_inches='tight')
plt.show()

print("Графики сохранены в файл 'ab_test_results.png'")

# Дополнительный график: динамика по дням
plt.figure(figsize=(15, 8))

df['date'] = df['time_order'].dt.date
daily_metrics = df.groupby(['date', 'test_group']).agg({
    'has_assign': ['count', 'sum']
}).reset_index()

daily_metrics.columns = ['date', 'test_group', 'total_orders', 'assigned_orders']
daily_metrics['assign_rate'] = daily_metrics['assigned_orders'] / daily_metrics['total_orders'] * 100

# График назначаемости по дням
plt.subplot(2, 1, 1)
for group in [0, 1]:
    group_data = daily_metrics[daily_metrics['test_group'] == group]
    label = f'Группа {"A" if group == 0 else "B"} ({3 if group == 0 else 5} км)'
    plt.plot(group_data['date'], group_data['assign_rate'], 
             marker='o', linewidth=2, label=label, color=colors[group])

plt.xlabel('Дата')
plt.ylabel('Назначаемость (%)')
plt.title('Динамика назначаемости по дням', fontsize=16, fontweight='bold')
plt.legend(fontsize=12)
plt.grid(alpha=0.3)
plt.xticks(rotation=45)

# График количества заказов по дням
plt.subplot(2, 1, 2)
for group in [0, 1]:
    group_data = daily_metrics[daily_metrics['test_group'] == group]
    label = f'Группа {"A" if group == 0 else "B"} ({3 if group == 0 else 5} км)'
    plt.plot(group_data['date'], group_data['total_orders'], 
             marker='s', linewidth=2, label=label, color=colors[group])

plt.xlabel('Дата')
plt.ylabel('Количество заказов')
plt.title('Количество заказов по дням', fontsize=16, fontweight='bold')
plt.legend(fontsize=12)
plt.grid(alpha=0.3)
plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig('daily_dynamics.png', dpi=300, bbox_inches='tight')
plt.show()

print("График динамики сохранен в файл 'daily_dynamics.png'")

# Итоговая сводка
print("\n" + "="*80)
print("                    ИТОГОВЫЕ РЕЗУЛЬТАТЫ A/B ТЕСТА")
print("="*80)
print(f"🎯 НАЗНАЧАЕМОСТЬ: Группа A: {assign_rate_a*100:.2f}% → Группа B: {assign_rate_b*100:.2f}%")
print(f"📈 УЛУЧШЕНИЕ: +{(assign_rate_b-assign_rate_a)*100:.2f} п.п. ({((assign_rate_b/assign_rate_a-1)*100):.1f}%)")
print(f"✅ СТАТИСТИЧЕСКИ ЗНАЧИМО: p < 0.001")
print(f"💰 ДОПОЛНИТЕЛЬНАЯ ВЫРУЧКА: ~{((assign_rate_b-assign_rate_a)*len(group_b)*500):.0f}₽")
print("="*80)
